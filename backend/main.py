import os
import logging
from dotenv import load_dotenv
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import Optional, List
from fastapi.middleware.cors import CORSMiddleware

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), ".env")
load_dotenv(dotenv_path=dotenv_path)

# Import direct processing modules
from models.job_models import (
    CreateRenderJobRequest,
    CreateTopicRenderJobRequest,
    DirectJobResponse,
    RenderJobData,
    JobStatus,
    TopicRenderJobData,
    JobRequest,
    JobType,
)
from workers.job_processor import JobProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ─── App Initialization ────────────────────────────────────────────────────────
app = FastAPI(
    title="ClarifAI Render API", description="Direct video rendering API"
)


# Add CORS middleware to allow requests from anywhere
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

os.makedirs("media", exist_ok=True)

# Initialize job processor
job_processor = JobProcessor()

# Mount static files for serving rendered videos
app.mount("/media", StaticFiles(directory="media"), name="media")


# ─── Routes ────────────────────────────────────────────────────────────────────
@app.get("/")
async def root():
    return {
        "message": "Welcome to the ClarifAI Render API with Direct Processing. Use POST /render or /batch_render to process jobs immediately."
    }


@app.post("/render", response_model=DirectJobResponse)
async def process_render_job(req: CreateRenderJobRequest) -> DirectJobResponse:
    """Process a render job immediately."""
    try:
        # Create job request
        job_request = JobRequest(
            job_type=JobType.RENDER,
            render_data=RenderJobData(
                script=req.script,
                scene_name=req.scene_name,
                entry_id=req.entry_id,
            ),
        )

        logger.info(f"Processing render job {job_request.job_id} immediately")

        # Process job directly
        result = job_processor.process_job(job_request)

        if result.success:
            return DirectJobResponse(
                job_id=job_request.job_id,
                status=JobStatus.COMPLETED,
                message="Job completed successfully.",
                result=result,
            )
        else:
            return DirectJobResponse(
                job_id=job_request.job_id,
                status=JobStatus.FAILED,
                message=f"Job failed: {result.error_message}",
                result=result,
            )

    except Exception as e:
        logger.error(f"Failed to process render job: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process job: {str(e)}")


@app.post("/batch_render", response_model=DirectJobResponse)
async def process_batch_render_job(
    req: CreateTopicRenderJobRequest,
    background_tasks: BackgroundTasks
) -> DirectJobResponse:
    """Process a topic-based batch render job immediately (background)."""
    try:
        # Create TopicRenderJobData from the new request format
        topic_render_data = TopicRenderJobData(
            topic_name=req.topic_name,
            entry_id=req.entry_id,
            scripts=req.scripts
        )

        # Create job request
        job_request = JobRequest(
            job_type=JobType.TOPIC_RENDER,
            topic_render_data=topic_render_data,
        )

        logger.info(f"Queueing topic render job {job_request.job_id} for topic: {req.topic_name}")

        # Process job in background
        def process_job_bg(job_request):
            try:
                result = job_processor.process_job(job_request)
                if result.success:
                    logger.info(f"Topic render job {job_request.job_id} completed successfully.")
                else:
                    logger.error(f"Topic render job {job_request.job_id} failed: {result.error_message}")
            except Exception as e:
                logger.error(f"Background job failed: {e}")

        background_tasks.add_task(process_job_bg, job_request)

        # Immediately return success response
        return DirectJobResponse(
            job_id=job_request.job_id,
            status='processing',
            message=f"Topic render job for '{req.topic_name}' queued for background processing.",
            result=None,
        )

    except Exception as e:
        logger.error(f"Failed to queue topic render job: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to queue job: {str(e)}")


# ─── Health Check Endpoint ─────────────────────────────────────────────────────
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "message": "ClarifAI Render API is running"}


# Removed queue-related endpoints as they are not needed for direct processing


# All queue-related endpoints removed for direct processing


# Removed failed jobs and cleanup endpoints as they are not needed for direct processing


# ─── Test Endpoint ─────────────────────────────────────────────────────────────
@app.post("/debug/test")
async def debug_test():
    """Debug endpoint to test direct processing."""
    try:
        # Create a test job
        job_request = JobRequest(
            job_type=JobType.RENDER,
            render_data=RenderJobData(
                script="""from manim import *

class DebugTest(Scene):
    def construct(self):
        text = Text("Debug Test")
        self.play(Write(text))
        self.wait(1)
""",
                scene_name="DebugTest",
            ),
        )

        # Process the job directly
        result = job_processor.process_job(job_request)

        return {
            "message": "Direct job processing test completed",
            "job_id": job_request.job_id,
            "result": result.model_dump(),
        }

    except Exception as e:
        logger.error(f"Failed to test processing: {e}")
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Processing test failed: {str(e)}")
