"""
Robust retry utilities for bulletproof backend operations.
Implements infinite retries with exponential backoff for critical operations.
"""

import time
import logging
import functools
from typing import Callable, Any, Optional, Type, Union, List
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


def infinite_retry(
    delay: float = 3.0,
    max_delay: float = 60.0,
    backoff_factor: float = 1.5,
    exceptions: Union[Type[Exception], tuple] = Exception,
    operation_name: str = "operation"
):
    """
    Decorator for infinite retries with exponential backoff.
    
    Args:
        delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        backoff_factor: Factor to multiply delay by after each failure
        exceptions: Exception types to catch and retry on
        operation_name: Name of the operation for logging
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            current_delay = delay
            attempt = 1
            start_time = datetime.now(timezone.utc)
            
            while True:
                try:
                    logger.info(f"🔄 Attempting {operation_name} (attempt #{attempt})")
                    result = func(*args, **kwargs)
                    
                    if attempt > 1:
                        elapsed = (datetime.now(timezone.utc) - start_time).total_seconds()
                        logger.info(f"✅ {operation_name} succeeded after {attempt} attempts in {elapsed:.2f}s")
                    
                    return result
                    
                except exceptions as e:
                    elapsed = (datetime.now(timezone.utc) - start_time).total_seconds()
                    logger.warning(
                        f"❌ {operation_name} failed (attempt #{attempt}, elapsed: {elapsed:.2f}s): {str(e)}"
                    )
                    
                    logger.info(f"⏳ Retrying {operation_name} in {current_delay:.1f} seconds...")
                    time.sleep(current_delay)
                    
                    # Exponential backoff with max delay cap
                    current_delay = min(current_delay * backoff_factor, max_delay)
                    attempt += 1
                    
        return wrapper
    return decorator


def retry_with_cleanup(
    cleanup_func: Optional[Callable] = None,
    delay: float = 3.0,
    max_delay: float = 60.0,
    backoff_factor: float = 1.5,
    exceptions: Union[Type[Exception], tuple] = Exception,
    operation_name: str = "operation"
):
    """
    Decorator for infinite retries with cleanup function on each failure.
    
    Args:
        cleanup_func: Function to call before each retry
        delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        backoff_factor: Factor to multiply delay by after each failure
        exceptions: Exception types to catch and retry on
        operation_name: Name of the operation for logging
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            current_delay = delay
            attempt = 1
            start_time = datetime.now(timezone.utc)
            
            while True:
                try:
                    logger.info(f"🔄 Attempting {operation_name} (attempt #{attempt})")
                    result = func(*args, **kwargs)
                    
                    if attempt > 1:
                        elapsed = (datetime.now(timezone.utc) - start_time).total_seconds()
                        logger.info(f"✅ {operation_name} succeeded after {attempt} attempts in {elapsed:.2f}s")
                    
                    return result
                    
                except exceptions as e:
                    elapsed = (datetime.now(timezone.utc) - start_time).total_seconds()
                    logger.warning(
                        f"❌ {operation_name} failed (attempt #{attempt}, elapsed: {elapsed:.2f}s): {str(e)}"
                    )
                    
                    # Run cleanup function if provided
                    if cleanup_func:
                        try:
                            logger.info(f"🧹 Running cleanup for {operation_name}")
                            cleanup_func()
                        except Exception as cleanup_error:
                            logger.warning(f"⚠️ Cleanup failed: {cleanup_error}")
                    
                    logger.info(f"⏳ Retrying {operation_name} in {current_delay:.1f} seconds...")
                    time.sleep(current_delay)
                    
                    # Exponential backoff with max delay cap
                    current_delay = min(current_delay * backoff_factor, max_delay)
                    attempt += 1
                    
        return wrapper
    return decorator


class RetryableOperation:
    """
    Context manager for retryable operations with automatic cleanup.
    """
    
    def __init__(
        self,
        operation_name: str,
        cleanup_funcs: Optional[List[Callable]] = None,
        delay: float = 3.0,
        max_delay: float = 60.0,
        backoff_factor: float = 1.5
    ):
        self.operation_name = operation_name
        self.cleanup_funcs = cleanup_funcs or []
        self.delay = delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.current_delay = delay
        self.attempt = 1
        self.start_time = datetime.now(timezone.utc)
        
    def __enter__(self):
        logger.info(f"🔄 Starting {self.operation_name} (attempt #{self.attempt})")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            elapsed = (datetime.now(timezone.utc) - self.start_time).total_seconds()
            logger.warning(
                f"❌ {self.operation_name} failed (attempt #{self.attempt}, elapsed: {elapsed:.2f}s): {str(exc_val)}"
            )
            
            # Run cleanup functions
            for cleanup_func in self.cleanup_funcs:
                try:
                    logger.info(f"🧹 Running cleanup for {self.operation_name}")
                    cleanup_func()
                except Exception as cleanup_error:
                    logger.warning(f"⚠️ Cleanup failed: {cleanup_error}")
            
            logger.info(f"⏳ Retrying {self.operation_name} in {self.current_delay:.1f} seconds...")
            time.sleep(self.current_delay)
            
            # Exponential backoff with max delay cap
            self.current_delay = min(self.current_delay * self.backoff_factor, self.max_delay)
            self.attempt += 1
            
            # Suppress the exception to continue retrying
            return True
        else:
            if self.attempt > 1:
                elapsed = (datetime.now(timezone.utc) - self.start_time).total_seconds()
                logger.info(f"✅ {self.operation_name} succeeded after {self.attempt} attempts in {elapsed:.2f}s")


def ensure_directory_exists(directory_path: str) -> None:
    """
    Ensure directory exists with infinite retries.
    """
    import os
    
    @infinite_retry(operation_name=f"create directory {directory_path}")
    def create_dir():
        os.makedirs(directory_path, exist_ok=True)
        if not os.path.exists(directory_path):
            raise OSError(f"Failed to create directory: {directory_path}")
    
    create_dir()


def safe_file_operation(operation: Callable, operation_name: str, cleanup_files: Optional[List[str]] = None) -> Any:
    """
    Execute file operation with infinite retries and cleanup.
    
    Args:
        operation: Function to execute
        operation_name: Name for logging
        cleanup_files: List of files to clean up on failure
    
    Returns:
        Result of the operation
    """
    def cleanup():
        if cleanup_files:
            import os
            for file_path in cleanup_files:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.info(f"🗑️ Cleaned up file: {file_path}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to clean up {file_path}: {e}")
    
    @retry_with_cleanup(
        cleanup_func=cleanup,
        operation_name=operation_name
    )
    def execute():
        return operation()
    
    return execute()
