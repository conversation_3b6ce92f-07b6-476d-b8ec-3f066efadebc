import os
import subprocess
import tempfile
import uuid
import re
import shutil
import logging
import textwrap
import requests
import json
import time
from typing import Optional, List, Tuple
from dotenv import load_dotenv
from config.queue_config import get_queue_config

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

logger = logging.getLogger(__name__)


class VideoRenderingService:
    """Service for rendering Manim videos and extracting scene information."""

    def __init__(self):
        self.config = get_queue_config()
        # Use absolute path for media directory
        self.base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
        self.video_dir = os.path.join(self.base_dir, "media", "videos")
        os.makedirs(self.video_dir, exist_ok=True)
        logger.info(f"Video directory set to: {self.video_dir}")

    def render_manim_video(self, code_str: str, video_id: str, max_retries: int = 3) -> Tuple[Optional[str], Optional[str]]:
        """
        Render a Manim video from code string with INFINITE retries and bugfix integration.
        This method will NEVER give up until a video is successfully rendered.
        """
        self._cleanup_partial_files(video_id)
        current_code = code_str
        last_error = None
        attempt = 0

        # TRUE INFINITE RETRIES - NO LIMITS, NO RESTRICTIONS
        while True:
            attempt += 1
            logger.info(f"🎬 Rendering attempt {attempt} for video {video_id}")

            # Write current code to file
            py_file = os.path.abspath(f"{video_id}.py")
            with open(py_file, "w", encoding="utf-8") as f:
                f.write(textwrap.dedent(current_code.strip()))

            try:
                result = subprocess.run(
                    ["manim", "-qk", "-o", f"{video_id}.mp4", py_file,
                    self._extract_scene_name(current_code), "--media_dir", os.path.abspath("media")],
                    capture_output=True,
                    text=True,
                    check=True,
                )

                video_path = self._find_rendered_video(video_id)
                if video_path:
                    logger.info(f"✅ SUCCESS! Video rendered after {attempt} attempts: {video_path}")
                    return video_path, py_file
                else:
                    logger.error(f"❌ Video file not found after successful manim run, retrying...")
                    time.sleep(3)
                    continue

            except subprocess.CalledProcessError as e:
                error_output = f"{e.stdout}\n{e.stderr}".strip()
                last_error = self._clean_error_message(error_output)

                # Ensure we have valid error message
                if not last_error or not last_error.strip():
                    last_error = f"Manim render failed with return code {e.returncode}"

                logger.error(f"❌ Render attempt {attempt} failed: {last_error}")

                # Try to fix the code using bugfix API - INFINITE RETRIES
                logger.info(f"🔧 Attempting to fix code using bugfix API...")
                logger.info(f"🔧 Current code length: {len(current_code)} chars")
                logger.info(f"🔧 Error message: {last_error[:200]}...")

                # Validate inputs before calling bugfix API
                if not current_code or not current_code.strip():
                    logger.error(f"🔧 Cannot call bugfix API: current_code is empty")
                    time.sleep(3)
                    continue

                if not last_error or not last_error.strip():
                    logger.error(f"🔧 Cannot call bugfix API: last_error is empty")
                    time.sleep(3)
                    continue

                # Call bugfix API - keep trying until we get different code
                fixed_code = self._call_bugfix_api(current_code, last_error)

                if fixed_code and fixed_code != current_code:
                    logger.info(f"🛠️ Received fixed code from API ({len(fixed_code)} chars), trying again...")
                    logger.info(f"🛠️ Code changed: {current_code[:100]}... -> {fixed_code[:100]}...")
                    current_code = fixed_code
                else:
                    if not fixed_code:
                        logger.warning(f"⚠️ Bugfix API returned no code, retrying with same code...")
                    elif fixed_code == current_code:
                        logger.warning(f"⚠️ Bugfix API returned same code, retrying...")
                    else:
                        logger.warning(f"⚠️ Bugfix API response issue, retrying with same code...")

                # 3-second delay before retry
                time.sleep(3)
                continue

            except Exception as e:
                logger.error(f"❌ Unexpected error: {str(e)}")
                last_error = str(e)
                # 3-second delay before retry
                time.sleep(3)
                continue
    
    def _clean_error_message(self, error: str) -> str:
        """Clean and format error message for API."""
        if not error or not error.strip():
            return "Unknown error occurred during rendering"

        # Remove file paths and other noise
        cleaned_error = re.sub(r'File ".*?/([^/]+\.py)"', r'File "\1"', error)

        # Keep only the most relevant part
        lines = cleaned_error.split('\n')
        if len(lines) > 10:  # If error is too long
            result = '\n'.join(lines[-10:])  # Last 10 lines usually most relevant
        else:
            result = cleaned_error

        # Ensure we never return empty string
        if not result or not result.strip():
            return "Error message could not be parsed"

        return result.strip()

    def _save_debug_files(self, video_id: str, code: str, error: str):
        """Save debug files for investigation."""
        debug_dir = os.path.join(self.base_dir, "debug")
        os.makedirs(debug_dir, exist_ok=True)
        
        with open(os.path.join(debug_dir, f"{video_id}_code.py"), "w") as f:
            f.write(code)
        with open(os.path.join(debug_dir, f"{video_id}_error.txt"), "w") as f:
            f.write(error)
        logger.info(f"Saved debug files for {video_id} in {debug_dir}")

    def _extract_scene_name(self, code_str: str) -> str:
        """Extract the scene class name from Manim code."""
        match = re.search(r"class\s+(\w+)\s*\(.*Scene.*\):", code_str)
        if match:
            return match.group(1)
        
        # Fallback: look for any class that inherits from Scene
        lines = code_str.splitlines()
        for line in lines:
            if line.strip().startswith("class ") and "Scene" in line:
                class_name = line.split("(")[0].replace("class ", "").strip()
                if class_name:
                    return class_name
        
        return "AutoScene"

    def _find_rendered_video(self, video_id: str) -> Optional[str]:
        """Find the rendered video file in the media directory."""
        # Check multiple possible locations
        possible_locations = [
            os.path.join(self.video_dir, f"{video_id}.mp4"),
            os.path.join(self.video_dir, video_id, "1080p60", f"{video_id}.mp4"),
            os.path.join(self.video_dir, video_id, "2160p60", f"{video_id}.mp4"),
            os.path.join("media", "videos", f"{video_id}.mp4"),
            os.path.join("media", "videos", video_id, "1080p60", f"{video_id}.mp4"),
            os.path.join("media", "videos", video_id, "2160p60", f"{video_id}.mp4"),
            os.path.join(os.getcwd(), "media", "videos", f"{video_id}.mp4"),
            os.path.join(os.getcwd(), "media", "videos", video_id, "1080p60", f"{video_id}.mp4"),
            os.path.join(os.getcwd(), "media", "videos", video_id, "2160p60", f"{video_id}.mp4"),
        ]

        for path in possible_locations:
            if os.path.exists(path):
                return os.path.abspath(path)
        
        return None

    def get_media_duration(self, path: str) -> float:
        """Get the duration of a media file using ffprobe."""
        try:
            result = subprocess.run(
                [
                    "ffprobe",
                    "-v", "error",
                    "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    path,
                ],
                capture_output=True,
                text=True,
                check=True,
            )
            return float(result.stdout.strip())
        except Exception as e:
            logger.warning(f"Could not get duration for {path}: {e}")
            return 0.0

    def _call_bugfix_api(self, code: str, error: str) -> Optional[str]:
        """Call the bugfix API with infinite retries until success."""
        frontend_url = os.getenv("FRONTEND_API_BASE_URL", "http://localhost:3000")

        # Validate inputs before making API call
        if not code or not code.strip():
            logger.error(f"🔧 Invalid code parameter: empty or None")
            return None

        if not error or not error.strip():
            logger.error(f"🔧 Invalid error parameter: empty or None")
            return None

        # INFINITE RETRIES for bugfix API as well
        api_attempt = 0
        while True:
            api_attempt += 1
            try:
                logger.info(f"🔧 Calling bugfix API at: {frontend_url}/api/bugfix (attempt {api_attempt})")
                logger.info(f"🔧 Code length: {len(code)} chars")
                logger.info(f"🔧 Error length: {len(error)} chars")

                # Prepare payload with validation
                payload = {
                    "code": code.strip(),
                    "error": error.strip()
                }

                logger.info(f"🔧 Payload prepared: code={len(payload['code'])} chars, error={len(payload['error'])} chars")

                response = requests.post(
                    f"{frontend_url}/api/bugfix",
                    json=payload,
                    headers={"Content-Type": "application/json"},
                )

                logger.info(f"🔧 Bugfix API response status: {response.status_code}")

                if response.status_code == 200:
                    data = response.json()
                    fixed_code = data.get("code", "").strip()
                    logger.info(f"🔧 Bugfix API returned {len(fixed_code)} characters")
                    return fixed_code
                else:
                    logger.error(f"🔧 Bugfix API error: {response.status_code} - {response.text}")
                    logger.error(f"🔧 Request payload was: {payload}")
                    logger.warning(f"🔧 Retrying bugfix API in 3 seconds...")
                    time.sleep(3)
                    continue

            except requests.exceptions.ConnectionError:
                logger.error(f"🔧 Bugfix API connection error - retrying in 5 seconds...")
                time.sleep(5)
                continue
            except Exception as e:
                logger.error(f"🔧 Bugfix API call failed: {str(e)} - retrying in 3 seconds...")
                time.sleep(3)
                continue



    def _cleanup_partial_files(self, video_id: str, py_file: str = None):
        """Clean up partial files after failed render."""
        try:
            logger.info(f"🧹 Cleaning up partial files for {video_id}")

            # Remove Python file if specified
            if py_file and os.path.exists(py_file):
                os.remove(py_file)
                logger.debug(f"Removed Python file: {py_file}")

            # Also remove any Python files that match the video_id pattern
            py_file_pattern = f"{video_id}.py"
            if os.path.exists(py_file_pattern):
                os.remove(py_file_pattern)
                logger.debug(f"Removed Python file: {py_file_pattern}")

            # Remove any partial video files in media/videos
            if os.path.exists(self.video_dir):
                for root, dirs, files in os.walk(self.video_dir):
                    for file in files:
                        if video_id in file:
                            file_path = os.path.join(root, file)
                            os.remove(file_path)
                            logger.debug(f"Removed partial video file: {file_path}")

            # Remove video folder if it exists
            video_folder = os.path.join("media", "videos", video_id)
            if os.path.exists(video_folder):
                shutil.rmtree(video_folder)
                logger.debug(f"Removed video folder: {video_folder}")

        except Exception as e:
            logger.warning(f"Failed to cleanup partial files for {video_id}: {e}")

    def cleanup_temp_files(self, py_file: str, video_id: str):
        """Clean up temporary files created during rendering."""
        try:
            # Remove Python file
            if py_file and os.path.exists(py_file):
                os.remove(py_file)

            # Remove video folder
            video_folder = os.path.join("media", "videos", video_id)
            if os.path.exists(video_folder):
                shutil.rmtree(video_folder)

        except Exception as e:
            logger.warning(f"Failed to cleanup temp files for {video_id}: {e}")


# Global service instance
_video_rendering_service: Optional[VideoRenderingService] = None


def get_video_rendering_service() -> VideoRenderingService:
    """Get the global VideoRenderingService instance."""
    global _video_rendering_service
    if _video_rendering_service is None:
        _video_rendering_service = VideoRenderingService()
    return _video_rendering_service
