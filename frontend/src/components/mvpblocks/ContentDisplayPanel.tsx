"use client";

import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Co<PERSON>, FileText, Code2, Check } from "lucide-react";
import { useState, useEffect } from "react";
import { a11yDark } from "react-syntax-highlighter/dist/esm/styles/hljs";
import dynamic from "next/dynamic";
import { ScrollingTextAnimation } from "../shared/ScrollingtextAnimation";

const LetterGlitch = dynamic(() => import("../shared/LetterGlitch"), {
  ssr: false,
});
const SyntaxHighlighter = dynamic(() => import("react-syntax-highlighter"), {
  ssr: false,
});

const sampleText = `
Manim code generation is in progress... Please wait... By the way, do you know that <PERSON><PERSON> is an open-source Python library for creating mathematical animations? It was developed by 3Blue1Brown and is used to create the animations you see in their popular math videos on YouTube. Manim is a powerful tool for educators, students, and anyone who wants to communicate complex ideas through animation. It's also a great way to learn Python and mathematics at the same time!
`;

interface ScriptItem {
  title: string;
  description: string;
  code?: string;
}

interface ContentDisplayPanelProps {
  isGenerating: boolean;
  currentScripts: ScriptItem[];
  manimStreams?: Record<string, string>;
}

export default function ContentDisplayPanel({
  isGenerating,
  currentScripts,
}: ContentDisplayPanelProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [showGenerationAnimation, setShowGenerationAnimation] = useState(true);
  const [animationKey, setAnimationKey] = useState(0);

  // Reset animation when scripts change to refresh the text
  useEffect(() => {
    if (currentScripts.length > 0) {
      setAnimationKey((prev) => prev + 1);
    }
  }, [currentScripts.length]);

  // Reset animation state when generation starts
  useEffect(() => {
    if (isGenerating) {
      setShowGenerationAnimation(true);
    }
  }, [isGenerating]);

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  // Generate dynamic text from current scripts
  const generateDynamicText = () => {
    if (currentScripts.length === 0) {
      return sampleText;
    }

    return currentScripts
      .map((script, index) => {
        let content = `Scene ${index + 1}: ${script.title}\n\n${
          script.description
        }`;
        if (script.code) {
          content += `\n\nAnimation Code:\n${script.code.substring(0, 200)}...`;
        }
        return content;
      })
      .join("\n\n---\n\n");
  };

  // Check if generation is complete (has scripts but still generating)
  const isGenerationComplete =
    currentScripts.length > 0 && currentScripts.every((script) => script.code);

  if (!isGenerating) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="flex flex-col col-span-1 lg:col-span-5 h-full border-l border-dashed bg-background"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: 20 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <div className="px-6 py-4 border-b border-dashed bg-background">
          <h3 className="text-lg">Generated Content</h3>
        </div>

        {/* Show generation animation when generating and no complete scripts */}
        {isGenerating && !isGenerationComplete && (
          <div className="h-[calc(94vh-var(--header-height))] relative">
            <LetterGlitch
              glitchColors={["#13a564a1", "#B75BF6", "#2089f9"]}
              glitchSpeed={40}
              centerVignette={true}
              outerVignette={false}
              smooth={true}
            />
            <ScrollingTextAnimation
              key={animationKey}
              text={generateDynamicText()}
              wordsPerLine={17}
              linesVisible={5}
              animationSpeed={500}
              className="rounded-2xl shadow-xl p-8 absolute inset-0"
              onComplete={() => {
                console.log("Animation completed!");
                if (isGenerationComplete) {
                  setShowGenerationAnimation(false);
                }
              }}
            />
          </div>
        )}

        {currentScripts.length > 0 &&
          (!isGenerating ||
            isGenerationComplete ||
            !showGenerationAnimation) && (
            <div className="space-y-4 flex-1 px-6 pb-20 mt-4 min-h-0">
              {currentScripts.map((script, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ delay: index * 0.1, duration: 0.3 }}
                >
                  <Card className="group bg-gradient-to-br from-secondary/5 to-secondary/20 hover:shadow-lg shadow-purple-100 dark:shadow-purple-950/30 duration-300">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg font-semibold bg-gradient-to-br from-foreground via-foreground/70 to-secondary bg-clip-text text-transparent">
                          {script.title}
                        </CardTitle>
                        <Badge
                          variant="secondary"
                          className="text-xs px-4 font-medium bg-primary/5 text-primary border-primary/20"
                        >
                          Scene {index + 1}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium mb-3 flex items-center text-muted-foreground">
                          <FileText className="w-4 h-4 mr-2 text-primary" />
                          Description
                        </h4>
                        <div className="bg-gradient-to-br from-secondary/30 to-secondary/10 rounded-xl p-4 border border-border/30">
                          <p className="text-sm text-foreground">
                            {script.description}
                          </p>
                        </div>
                      </div>
                      {script.code && (
                        <Accordion type="single" collapsible className="w-full">
                          <AccordionItem value="code">
                            <AccordionTrigger>
                              <div className="flex items-center space-x-2 px-4">
                                <Code2 className="w-4 h-4 text-green-400" />
                                <span>Animation Code</span>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-0 pb-0 rounded-xl overflow-hidden">
                              <div className="border-t border-border/30 ">
                                <div className="flex items-center justify-between px-4 py-2 bg-zinc-900/50">
                                  <span className="text-xs text-muted-foreground font-mono">
                                    Python • Manim
                                  </span>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() =>
                                      copyToClipboard(script.code!, index)
                                    }
                                    className="h-7 px-2 text-xs hover:bg-zinc-800/50"
                                  >
                                    {copiedIndex === index ? (
                                      <>
                                        <Check className="w-3 h-3 mr-1 text-green-400" />
                                        Copied
                                      </>
                                    ) : (
                                      <>
                                        <Copy className="w-3 h-3 mr-1" />
                                        Copy
                                      </>
                                    )}
                                  </Button>
                                </div>
                                <ScrollArea className="h-96 border border-t-0 rounded-b-xl overflow-hidden">
                                  <SyntaxHighlighter
                                    language="python"
                                    customStyle={{
                                      background: "transparent",
                                    }}
                                    style={a11yDark}
                                  >
                                    {script.code}
                                  </SyntaxHighlighter>
                                </ScrollArea>
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
      </motion.div>
    </AnimatePresence>
  );
}
