"use client";

import { useState } from "react";
import { useAuthUser } from "@/hooks/useAuthUser";
import InputSection from "./InputSection";
import TaskProgressSidebar from "./TaskProgressSidebar";
import ContentDisplayPanel from "./ContentDisplayPanel";
import VideoConfirmationDialog from "./VideoConfirmationDialog";



interface Task {
  id: string;
  name: string;
  status: "pending" | "in-progress" | "completed" | "failed";
}

interface ScriptItem {
  title: string;
  description: string;
  code?: string;
}

export default function Bolt() {
  const { user } = useAuthUser();
  const [isGenerating, setIsGenerating] = useState(false);
  const [showGenerationUI, setShowGenerationUI] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [currentScripts, setCurrentScripts] = useState<ScriptItem[]>([]);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [videoJobId, setVideoJobId] = useState<string | null>(null);
  const [queuePosition, setQueuePosition] = useState<number | null>(null);

  // Simple sequential generation function
  const generateVideoSequence = async (prompt: string) => {
    try {
      console.log("🚀 Starting video generation sequence for:", prompt);

      // Step 1: Generate Scripts
      console.log("📝 Step 1: Generating scripts...");
      setTasks([
        { id: "1", name: "Analyzing Input", status: "in-progress" },
        { id: "2", name: "Generating Script", status: "pending" },
      ]);

      const scriptResponse = await fetch("/api/generate-script", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt }),
      });

      if (!scriptResponse.ok) {
        throw new Error(`Script generation failed: ${scriptResponse.status}`);
      }

      const scripts: ScriptItem[] = await scriptResponse.json();
      console.log(`✅ Generated ${scripts.length} scripts`);
      setCurrentScripts(scripts);

      setTasks((prev) =>
        prev.map((task) =>
          task.id === "1"
            ? { ...task, status: "completed" }
            : task.id === "2"
              ? { ...task, status: "in-progress" }
              : task
        )
      );

      // Step 2: Generate Manim Codes
      console.log("🎨 Step 2: Generating manim codes...");
      const scriptsWithCode = [];

      for (let i = 0; i < scripts.length; i++) {
        // Add Generating Manim task for this script
        const manimTaskId = (3 + i).toString();
        setTasks((prev) => [
          ...prev,
          {
            id: manimTaskId,
            name: `Generating Manim for script ${i + 1}`,
            status: "in-progress",
          },
        ]);

        const script = scripts[i];
        console.log(
          `🎨 Generating manim code for: ${script.title} (${i + 1}/${scripts.length})`
        );

        const manimResponse = await fetch("/api/manim", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            schema: { title: script.title, description: script.description },
            mainTheme: prompt,
          }),
        });

        if (manimResponse.ok) {
          const manimData = await manimResponse.json();
          if (manimData.code) {
            scriptsWithCode.push({ ...script, code: manimData.code });
            console.log(`✅ Manim code generated for: ${script.title}`);
          } else {
            console.warn(`⚠️ No code returned for: ${script.title}`);
          }
        } else {
          console.error(`❌ Manim generation failed for: ${script.title}`);
        }

        // Set this Manim task to completed
        setTasks((prev) =>
          prev.map((task) =>
            task.id === manimTaskId ? { ...task, status: "completed" } : task
          )
        );
      }

      console.log(
        `✅ Generated manim codes for ${scriptsWithCode.length}/${scripts.length} scripts`
      );

      // Step 3: Generate Quiz
      // Add up Generating quiz to task list
      setTasks((prev) => [
        ...prev,
        {
          id: "100",
          name: "Generating Quiz",
          status: "in-progress",
        },
      ]);

      console.log("🧠 Step 3: Generating quiz...");
      const quizResponse = await fetch("/api/ai-quiz", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: scripts.map((s) => s.title).join(","),
          content: scripts.map((s) => s.description).join("\n"),
          userId: user.id,
          topicName: prompt
        }),
      });

      if (!quizResponse.ok) {
        throw new Error(`Quiz generation failed: ${quizResponse.status}`);
      }

      const quizData = await quizResponse.json();
      const quizId = quizData.id;
      console.log(`✅ Quiz generated with ID: ${quizId}`);

      // Set quiz task to completed
      setTasks((prev) =>
        prev.map((task) =>
          task.id === "100"
            ? { ...task, status: "completed" }
            : task
        )
      );

      // Step 4: Queue Video Generation
      if (scriptsWithCode.length > 0 && quizId) {
        console.log("📋 Step 4: Queueing video generation...");
        await queueVideoGeneration(scriptsWithCode, quizId, prompt);
      } else {
        throw new Error(
          `Cannot queue generation: ${scriptsWithCode.length} scripts with code, quiz ID: ${quizId}`
        );
      }
    } catch (error) {
      console.error("❌ Generation sequence failed:", error);
      setIsGenerating(false);
    }
  };

  // Queue video generation through start-generation API
  const queueVideoGeneration = async (
    scriptsWithCode: ScriptItem[],
    quizId: string,
    prompt: string
  ) => {
    try {
      console.log("📋 Queueing video generation...");

      // Prepare scripts data for queueing
      const scriptsData = scriptsWithCode.map((script) => ({
        manim_code: script.code!,
        description: script.description,
      }));

      console.log("📤 Sending queue request to start-generation API");

      const response = await fetch("/api/start-generation", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          entryId: quizId,
          scripts: scriptsData,
          topicName: prompt
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Queue error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log("✅ Video generation queued successfully:", result);

      setVideoJobId(quizId); // Use entryId as job ID
      setQueuePosition(result.queuePosition);
      setIsGenerating(false);
      setShowConfirmationDialog(true);
      console.log("🎉 Video generation sequence completed!");
    } catch (error) {
      console.error("❌ Video queueing failed:", error);
      setIsGenerating(false);
    }
  };

  // Simple function to handle generation click
  const handleGenerateClick = async (prompt: string) => {
    setIsGenerating(true);
    setShowGenerationUI(true);
    setCurrentScripts([]);

    if (typeof window !== "undefined") {
      localStorage.setItem("currentPrompt", prompt);
    }

    await generateVideoSequence(prompt);
  };

  const handleDialogClose = () => {
    setShowConfirmationDialog(false);
  };

  const handleResetToInput = () => {
    setShowConfirmationDialog(false);
    setShowGenerationUI(false);
    setIsGenerating(false);
    setCurrentScripts([]);
    setTasks([]);
  };

  return (
    <div className="h-full flex flex-col">
      <InputSection
        key={"input-section"}
        isGenerating={isGenerating}
        onGenerate={handleGenerateClick}
      />
      {showGenerationUI && (
        <div className="flex-1 w-full h-0 overflow-hidden">
          <div className="h-full w-full grid grid-cols-1 lg:grid-cols-7 gap-0">
            <TaskProgressSidebar isGenerating={isGenerating} tasks={tasks} />
            <ContentDisplayPanel
              isGenerating={isGenerating}
              currentScripts={currentScripts}
            />
          </div>
        </div>
      )}

      <VideoConfirmationDialog
        key={"video-confirmation-dialog"}
        isOpen={showConfirmationDialog}
        onClose={handleDialogClose}
        onContinueWorking={handleResetToInput}
        videoJobId={videoJobId}
        queuePosition={queuePosition}
      />
    </div>
  );
}
