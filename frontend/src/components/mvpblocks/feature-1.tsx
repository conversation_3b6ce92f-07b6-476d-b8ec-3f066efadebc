import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart3,
  <PERSON>hare2,
  <PERSON>ap,
} from "lucide-react";

const features = [
  {
    icon: <Brain className="h-6 w-6" />,
    title: "AI-to-Animation Generation",
    desc: "Convert plain English prompts into high-quality 2D/3D animations using Manim.",
  },
  {
    icon: <ListChecks className="h-6 w-6" />,
    title: "AI-Generated Quizzes",
    desc: "Reinforce learning with auto-generated quizzes tailored to each animation.",
  },
  {
    icon: <BarChart3 className="h-6 w-6" />,
    title: "Concept Understanding Analysis",
    desc: "Get feedback on your clarity of understanding with AI-based performance analytics.",
  },
  {
    icon: <Clapperboard className="h-6 w-6" />,
    title: "Video Library & Editor",
    desc: "Create, play, edit, and manage all your animated videos in one organized place.",
  },
  {
    icon: <Share2 className="h-6 w-6" />,
    title: "Social Sharing",
    desc: "Share your animations across platforms with your team, classmates, or the world.",
  },
  {
    icon: <Zap className="h-6 w-6" />,
    title: "Manim-Powered Quality",
    desc: "Professional-grade animations using the same technology used by 3Blue1Brown.",
  },
];

export default function Feature1() {
  return (
    <section className="relative py-14">
      <div className="mx-auto max-w-screen-xl px-4 md:px-8">
        <div className="relative mx-auto max-w-2xl sm:text-center">
          <div className="relative z-10 text-center">
            <h3 className="font-geist mt-4 text-3xl font-normal tracking-tighter sm:text-4xl md:text-5xl">
              Powerful Features for Effortless Learning
            </h3>
            <p className="font-geist mt-3 text-foreground/60">
              Discover how ClarifAI transforms complex topics into clear,
              engaging animations and learning materials powered by AI and
              Manim.
            </p>
          </div>
          <div
            className="absolute inset-0 mx-auto h-44 max-w-xs blur-[118px]"
            style={{
              background:
                "linear-gradient(152.92deg, rgba(192, 15, 202, 0.2) 4.54%, rgba(192, 11, 209, 0.26) 34.2%, rgba(192, 15, 202, 0.1) 77.55%)",
            }}
          ></div>
        </div>
        <hr className="mx-auto mt-5 h-px w-1/2 bg-foreground/30" />
        <div className="relative mt-12">
          <ul className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {features.map((item, idx) => (
              <li
                key={idx}
                className="transform-gpu space-y-3 rounded-xl border bg-transparent p-4 [box-shadow:0_-20px_80px_-20px_#d478ff2f_inset]"
              >
                <div className="w-fit transform-gpu rounded-full border p-4 text-primary [box-shadow:0_-20px_80px_-20px_#d478ff3f_inset] dark:[box-shadow:0_-20px_80px_-20px_#d478ff0f_inset]">
                  {item.icon}
                </div>
                <h4 className="font-geist text-lg font-bold tracking-tighter">
                  {item.title}
                </h4>
                <p className="text-gray-500">{item.desc}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}
