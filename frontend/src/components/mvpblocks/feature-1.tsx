import {
  Clapperboard,
  FileText,
  ListChecks,
  BarChart3,
  GraduationCap,
  Zap,
} from 'lucide-react';

const features = [
  {
    icon: <Clapperboard className="h-6 w-6" />,
    title: 'Text-to-Video Explainers',
    desc: 'Instantly generate short, animated explainers for any topic.',
  },
  {
    icon: <FileText className="h-6 w-6" />,
    title: 'Visual PDF Notes',
    desc: 'Clean, structured notes with visuals for quick and easy revision.',
  },
  {
    icon: <ListChecks className="h-6 w-6" />,
    title: 'Instant Q&A Sets',
    desc: 'Get topic-specific MCQs and short answers on the fly.',
  },
  {
    icon: <BarChart3 className="h-6 w-6" />,
    title: 'Performance Tracker',
    desc: 'Track what you’ve learned with smart analytics and feedback.',
  },
  {
    icon: <GraduationCap className="h-6 w-6" />,
    title: 'Syllabus-Aligned Content',
    desc: 'Personalized content mapped to your board, college, or prep goal.',
  },
  {
    icon: <Zap className="h-6 w-6" />,
    title: 'Instant, No Hassle',
    desc: 'No installs, no signups — just fast and focused learning.',
  },
];
export default function Feature1() {
  return (
    <section className="relative py-14">
      <div className="mx-auto max-w-screen-xl px-4 md:px-8">
        <div className="relative mx-auto max-w-2xl sm:text-center">
          <div className="relative z-10 text-center">
            <h3 className="font-geist mt-4 text-3xl font-normal tracking-tighter sm:text-4xl md:text-5xl">
              What Makes Learning Effortless
            </h3>
            <p className="font-geist mt-3 text-foreground/60">
              Discover the core tools that turn complex topics into clear, engaging, and personalized learning kits — built to help you grasp more in less time.
            </p>
          </div>
          <div
            className="absolute inset-0 mx-auto h-44 max-w-xs blur-[118px]"
            style={{
              background:
                'linear-gradient(152.92deg, rgba(192, 15, 202, 0.2) 4.54%, rgba(192, 11, 209, 0.26) 34.2%, rgba(192, 15, 202, 0.1) 77.55%)',
            }}
          ></div>
        </div>
        <hr className="mx-auto mt-5 h-px w-1/2 bg-foreground/30" />
        <div className="relative mt-12">
          <ul className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {features.map((item, idx) => (
              <li
                key={idx}
                className="transform-gpu space-y-3 rounded-xl border bg-transparent p-4 [box-shadow:0_-20px_80px_-20px_#d478ff2f_inset]"
              >
                <div className="w-fit transform-gpu rounded-full border p-4 text-primary [box-shadow:0_-20px_80px_-20px_#d478ff3f_inset] dark:[box-shadow:0_-20px_80px_-20px_#d478ff0f_inset]">
                  {item.icon}
                </div>
                <h4 className="font-geist text-lg font-bold tracking-tighter">
                  {item.title}
                </h4>
                <p className="text-gray-500">{item.desc}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}
