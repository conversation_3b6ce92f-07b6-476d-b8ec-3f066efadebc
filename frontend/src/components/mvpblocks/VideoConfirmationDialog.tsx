"use client";

import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CheckCircle2, Clock, Video, ArrowRight, ExternalLink } from "lucide-react";
import { useGeneration } from "@/contexts/GenerationContext";

interface VideoConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onContinueWorking?: () => void;
  videoJobId: string | null;
  queuePosition?: number | null;
}

export default function VideoConfirmationDialog({
  isOpen,
  onClose,
  onContinueWorking,
  videoJobId,
  queuePosition,
}: VideoConfirmationDialogProps) {
  const { state } = useGeneration();

  const handleGoToDashboard = () => {
    onClose();
    window.location.href = "/dashboard";
  };

  const handleGoToLibrary = () => {
    onClose();
    window.location.href = "/library";
  };

  const handleContinueWorking = () => {
    // Use custom handler if provided, otherwise just close
    if (onContinueWorking) {
      onContinueWorking();
    } else {
      onClose();
    }
  };

  // Check if video is completed
  const isCompleted = state.isCompleted && state.completedVideoUrl;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-400/20 to-emerald-500/10 rounded-full flex items-center justify-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              <CheckCircle2 className="w-8 h-8 text-green-500" />
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <DialogTitle>
              {isCompleted ? "Video Generation Complete!" : "Video Generation Started!"}
            </DialogTitle>
          </motion.div>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="space-y-4"
        >
          <DialogDescription className="text-muted-foreground leading-relaxed">
            Your animated educational video is now being generated. This process
            typically takes 3-5 minutes.
          </DialogDescription>

          {videoJobId && (
            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg p-4 border border-primary/10">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-secondary animate-pulse"></div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-foreground">
                    Job ID: {videoJobId}
                  </div>
                  {queuePosition && (
                    <div className="text-xs text-muted-foreground">
                      Queue Position: #{queuePosition}
                    </div>
                  )}
                  <div className="text-xs text-muted-foreground">
                    Track your video generation progress
                  </div>
                </div>
                <Video className="w-5 h-5 text-primary" />
              </div>
            </div>
          )}

          <div className="bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-lg p-4 border border-blue-500/10">
            <div className="flex items-start space-x-3">
              <Clock className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                <p className="text-sm font-medium text-foreground">
                  What happens next?
                </p>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Your video will be processed in the background</li>
                  <li>• You'll receive a notification when it's ready</li>
                  <li>• The video will be available in your dashboard</li>
                  <li>• You can download or share it directly</li>
                </ul>
              </div>
            </div>
          </div>
        </motion.div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          {isCompleted ? (
            <>
              <Button
                variant="outline"
                onClick={handleContinueWorking}
                className="w-full sm:w-auto"
              >
                Generate Another
              </Button>
              <Button onClick={handleGoToLibrary} className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600">
                <span>View in Library</span>
                <ExternalLink className="w-4 h-4 ml-2" />
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                onClick={handleContinueWorking}
                className="w-full sm:w-auto"
              >
                Continue Working
              </Button>
              <Button onClick={handleGoToDashboard}>
                <span>Go to Dashboard</span>
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
