import React from 'react';

const GradientBlur: React.FC = () => {
  return (
    <div className="absolute top-0 inset-0 z-0 pointer-events-none overflow-hidden">
      {/* Top left blur */}
      <div className="absolute -top-20 -left-20 w-[600px] h-[600px] bg-gradient-to-br from-violet-500/30 via-purple-500/20 to-transparent rounded-full blur-[100px] opacity-50" />

      {/* Top right blur */}
      <div className="absolute -top-40 -right-20 w-[500px] h-[500px] bg-gradient-to-bl from-purple-500/30 via-violet-500/20 to-transparent rounded-full blur-[100px] opacity-50" />

      {/* Bottom left blur */}
      <div className="absolute bottom-0 left-0 w-[400px] h-[400px] bg-gradient-to-tr from-purple-500/20 via-violet-500/10 to-transparent rounded-full blur-[80px] opacity-30" />
    </div>
  );
};

export default GradientBlur;