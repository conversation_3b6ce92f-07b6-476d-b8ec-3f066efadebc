import { generateText } from "ai";
import { google } from "@ai-sdk/google";
import { NextResponse } from "next/server";
import { groq } from "@ai-sdk/groq";

export const maxDuration = 40;

export async function POST(req: Request) {
  const {
    schema,
    mainTheme = "",
  }: {
    schema: { title: string; description: string };
    mainTheme?: string;
  } = await req.json();

  const { text } = await generateText({
    model: groq("llama-3.3-70b-versatile"),
    system: `You are a Manim expert coder. Create a clean, educational animation for the given concept, strictly following these Manim 0.19.0 rules:
1. Use 'self.wait()', never 'wait()'.
2. Use 'self.play(...)' for all animations, not 'self.add(...)'.
3. Always position/transform Mobjects (e.g., '.move_to()', '.to_edge()', '.scale()') before adding/animating.
4. Use 'Text' for regular text, 'Tex' for LaTeX (valid, raw strings, e.g., r'E = mc^{2}').
5. Never use double backslashes in LaTeX (e.g., r'\\frac{a}{b}').
6. Initialize all Mobjects before using methods like '.next_to()', '.shift()', etc.
7. Define a class inheriting from 'Scene' with 'def construct(self):'.
8. Use only Manim 0.19.0 syntax; avoid deprecated/old APIs.
9. Use 'Write' or 'FadeIn' for 'Text', not 'Create'.
10. Group animations (AnimationGroup/Succession) instead of multiple 'self.add(...)'.
11. Adjust position/scale/color before adding 'Text'/'Tex'.
12. Code must be clean, valid Python (no syntax/indent errors).
13. No external files (images/audio); use only built-in primitives.
14. Do not mix APIs from other Manim versions.
15. Use default/supported fonts only.
16. All animations must render cleanly with no errors.

Use only built-in primitives: Text, Tex, Circle, Square, Rectangle, Line, Arrow, Arc, Polygon, NumberLine, Axes, BarChart, Table, Group, VGroup, and their animations (FadeIn, FadeOut, Create, Uncreate, Write, Transform, Scale, Rotate, Shift). Never use SVGMobject, ImageMobject, VideoMobject, or external files.

Animation flow: Animated title, step-by-step element introduction, use shapes/colors/motion to explain, clean removal before new elements, end with summary. Style: Simple, visual, slow-paced, clean layout, no overlaps, color/movement for focus, minimal text, max visuals. Output only Python code for a single Scene class, no comments or markdown. Code must run with: 'manim -pql script_name.py ClassName'.`,
    prompt: `Create a Manim scene for the following concept of ${mainTheme}:\n\nTitle: ${schema.title}\nDescription: ${schema.description}`,
    maxTokens: 1200,
  });

  const cleanCode = text.replace(/```python\n/, "").replace(/```/, "");

  return NextResponse.json({ code: cleanCode });
}
