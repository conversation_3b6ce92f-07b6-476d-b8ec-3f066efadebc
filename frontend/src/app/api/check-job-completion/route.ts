import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function POST(request: NextRequest) {
  try {
    const { entryId } = await request.json();

    if (!entryId) {
      return NextResponse.json(
        { error: "Missing required parameter: entryId" },
        { status: 400 }
      );
    }

    console.log("🔍 Checking job completion status for entry:", entryId);

    // Check if entry exists and has videoUrl
    try {
      const entry = await db.entries.findUnique({
        where: { id: entryId },
        select: {
          id: true,
          videoUrl: true,
          isGenerating: true,
        },
      });

      if (!entry) {
        return NextResponse.json(
          { 
            error: "Entry not found",
            isCompleted: false,
            shouldSkip: true 
          },
          { status: 404 }
        );
      }

      // Check if job is already completed (has videoUrl)
      const isCompleted = !!entry.videoUrl;
      
      console.log(`📊 Job completion status for ${entryId}:`, {
        isCompleted,
        hasVideoUrl: !!entry.videoUrl,
        isGenerating: entry.isGenerating,
      });

      return NextResponse.json(
        {
          success: true,
          entryId,
          isCompleted,
          shouldSkip: isCompleted, // Skip if already completed
          hasVideoUrl: !!entry.videoUrl,
          isGenerating: entry.isGenerating,
        },
        { status: 200 }
      );
    } catch (dbError) {
      console.error("❌ Database query failed:", dbError);
      return NextResponse.json(
        { 
          error: "Failed to check job completion status",
          isCompleted: false,
          shouldSkip: false 
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("❌ Error checking job completion:", error);
    return NextResponse.json(
      { 
        error: "Internal server error",
        isCompleted: false,
        shouldSkip: false 
      },
      { status: 500 }
    );
  }
}
