import { generateText } from "ai";
import { NextResponse } from "next/server";
import { groq } from "@ai-sdk/groq";

export const maxDuration = 40;

export async function POST(req: Request) {
  const { code, error } = await req.json();

  if (!code || !error) {
    return NextResponse.json(
      { error: "Both code and error parameters are required" },
      { status: 400 }
    );
  }

  // Clean the error message to remove sensitive paths
  const cleanedError = error
    .replace(/File ".*?\/([^\/]+\.py)"/g, 'File "$1"')
    .replace(/\/home\/<USER>\/]+\//g, "~/")
    .replace(/\n\s*at.*/g, "");

  const { text } = await generateText({
    model: groq("meta-llama/llama-4-scout-17b-16e-instruct"),
    system: `You are a strict Manim v0.19.0 expert. Your task is to fix the given Manim code in Python fully given the erroneous Manim code and the related error message.

Guidelines you must always follow:

Most commonly ->

Analyze the LaTeX error from the Manim render log, identify missing packages (e.g., amsmath), syntax issues, or font problems in the Tex/MathTex strings. Suggest fixes like adding \\usepackage commands, simplifying the LaTeX code, or installing required LaTeX packages (e.g., texlive-latex-extra). If the error persists, recommend switching to basic Text or debugging with a minimal example like MathTex(r'\\sqrt{x}') to isolate the issue.

1. Always use 'self.wait()' instead of 'wait()' inside a Scene.
2. Use 'self.play(...)' for animations; do not use 'self.add(...)' unless absolutely necessary.
3. Define a class that inherits from 'Scene' and implement 'def construct(self):'.
4. Apply transformations (like '.move_to()', '.to_edge()', '.scale()') before adding the Mobject to the scene.
5. Use 'Text' for plain text and 'Tex' for LaTeX; choose appropriately.
6. All LaTeX content must be written using raw strings: r'...'.
7. Do not use double backslashes in LaTeX like '\\\\frac'; use '\\frac' instead.
8. Avoid syntax mistakes: colons, indentation, commas, brackets must be correct.
9. Do not use deprecated functions from older versions like 'scene.render()', 'display()', etc.
10. Ensure every object is initialized before it's used.
11. Use 'Write(...)', 'FadeIn(...)', or 'Create(...)' to animate object entry — never just 'add(...)'.
12. Do not apply 'Create(...)' on non-line/path objects like 'Text'; use 'Write(...)' instead.
13. Use 'AnimationGroup' or 'Succession' for grouping animations — avoid messy simultaneous plays.
14. Avoid calling 'self.add(...)' multiple times; favor animation chaining with 'self.play(...)'.
15. Never use invalid LaTeX expressions.
16. Scene must be runnable via: 'manim -pql script.py ClassName'.
17. Use meaningful variable names, no typos like 'txtt' or 'circlle'.
18. Keep code Pythonic and PEP8-compliant — proper spacing, indentation, and structure.
19. Maintain object order: define → transform → animate.
20. Do not transform Mobjects after they're animated — do it before.
21. Never use old 'manimlib' syntax.
22. Never use external resources — only built-in Manim primitives.
23. Never use unsupported fonts or SVG, Image, or Video Mobjects.
24. Do not return anything other than valid Python code.
25. The fixed code must render perfectly with no warnings or LaTeX errors in Manim v0.19.0.
26. In Manim v0.19.0, use 'self.camera.frame' is not available. Replace with 'self.camera' directly.
27. In Manim v0.19.0, import Scene from 'manim' is deprecated. Use 'from manim import Scene' instead.
28. Latex Errors are common. Make sure to fix them.
29. If you cant fix the error in the code, change the code totally. But make it perfectly renderable

Simple, visual, slow-paced, clean layout, no overlaps, color/movement for focus, minimal text, max visuals. Output only the correct Python code strictly following the above rules. with no comments or markdown and no additional text.`,
    prompt: `Fix the Manim code below using the error provided.  
Return only the corrected Python code — no explanation, no markdown, no comments.

NOTE: If the error is related to LaTeX failing to convert to DVI. This usually happens due to invalid LaTeX syntax, unescaped special characters, or unsupported LaTeX commands. Fix all LaTeX expressions used in Tex/MathTex by ensuring:
- You use raw strings: r"..."
- You use valid LaTeX syntax like \frac, \sqrt, \text, etc.
- You do not use special characters like #, %, &, _, etc. unless escaped.
- You use MathTex for math expressions and Tex for plain text with LaTeX formatting.
- You avoid double backslashes like '\\\\frac'; use '\\frac' instead.
- If \text is used, ensure required LaTeX packages are supported.

NOTE: Analyze the LaTeX error from the Manim render log, identify missing packages (e.g., amsmath), syntax issues, or font problems in the Tex/MathTex strings. Suggest fixes like adding \\usepackage commands, simplifying the LaTeX code, or installing required LaTeX packages (e.g., texlive-latex-extra). If the error persists, recommend switching to basic Text or debugging with a minimal example like MathTex(r'\\sqrt{x}') to isolate the issue.

Error: ${cleanedError}

Code:
${code}`,
    maxTokens: 4096,
  });

  // Extract code from response more robustly
  let correctedCode = text.trim();
  correctedCode = correctedCode
    .replace(/^```python\n?/, "")
    .replace(/\n```$/, "");
  correctedCode = correctedCode.replace(/^```\n?/, "").replace(/\n```$/, "");

  // Validate the response contains valid Python code
  if (
    !correctedCode.includes("class") ||
    !correctedCode.includes("Scene") ||
    !correctedCode.includes("def construct")
  ) {
    throw new Error("API did not return valid Manim code");
  }

  return NextResponse.json({ code: correctedCode });
}
