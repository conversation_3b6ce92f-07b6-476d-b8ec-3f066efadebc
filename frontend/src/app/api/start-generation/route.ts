import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";

const BACKEND_URL =
  process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { entryId, scripts, topicName } = await request.json();

    if (!entryId || !scripts || !topicName) {
      return NextResponse.json(
        { error: "Missing required fields: entryId, scripts, topicName" },
        { status: 400 }
      );
    }

    console.log("🚀 Starting generation for entry:", entryId);

    // MongoDB-compatible atomic queue position assignment
    let newQueuePosition: number = 0;
    let updatedEntry: any;

    // Retry loop to handle potential race conditions
    let retryCount = 0;
    const maxRetries = 10;

    while (retryCount < maxRetries) {
      try {
        // Get the highest queue position
        const highestQueueEntry = await db.entries.findFirst({
          where: {
            queuePosition: {
              not: -1, // Exclude completed entries
            },
          },
          orderBy: {
            queuePosition: "desc",
          },
          select: {
            queuePosition: true,
          },
        });

        // Calculate new queue position
        newQueuePosition = highestQueueEntry?.queuePosition
          ? highestQueueEntry.queuePosition + 1
          : 1;

        console.log(`📋 Attempting to assign queue position: ${newQueuePosition} (attempt ${retryCount + 1})`);

        // Try to update the entry with the calculated position
        updatedEntry = await db.entries.update({
          where: {
            id: entryId,
            userId: session.user.id, // Ensure user owns this entry
          },
          data: {
            isGenerating: true,
            queuePosition: newQueuePosition,
            scripts: scripts,
            topicName: topicName,
            updatedAt: new Date(),
          },
        });

        // If we get here, the update was successful
        console.log(`✅ Successfully assigned queue position: ${newQueuePosition}`);
        break;

      } catch (error: any) {
        retryCount++;
        if (retryCount >= maxRetries) {
          console.error(`❌ Failed to assign queue position after ${maxRetries} attempts:`, error);
          throw error;
        }

        // Wait a bit before retrying to reduce race condition chances
        await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
        console.log(`⚠️ Queue position assignment failed, retrying... (attempt ${retryCount + 1})`);
      }
    }

    console.log("✅ Entry queued with position:", newQueuePosition);

    // Return immediately with job ID and queue position
    const response = NextResponse.json({
      success: true,
      entryId: updatedEntry.id,
      queuePosition: newQueuePosition,
    });

    // If this is the first in queue, start backend processing asynchronously
    // This ensures we return the response immediately
    if (newQueuePosition === 1) {
      console.log(
        "🎬 First in queue - starting backend processing asynchronously"
      );
      // Start processing in background without awaiting
      startBackendProcessing(entryId, scripts, topicName).catch((error) => {
        console.error("❌ Background processing failed:", error);
      });
    }

    return response;
  } catch (error) {
    console.error("❌ Error starting generation:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function startBackendProcessing(
  entryId: string,
  scripts: any[],
  topicName: string
) {
  try {
    console.log(
      "📤 Sending batch render request to backend for entry:",
      entryId
    );

    // Prepare batch render payload
    const renderPayload = {
      topicName: topicName,
      entryId: entryId,
      scripts: scripts.map((script) => ({
        manim_code: script.manim_code,
        description: script.description,
      })),
      priority: 0,
    };

    const response = await fetch(`${BACKEND_URL}/batch_render`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(renderPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Backend error: ${response.status} - ${errorText}`);
      // DO NOT THROW - just log the error and continue
      console.error("❌ Backend processing failed, but job remains in queue for retry");
      return;
    }

    const result = await response.json();
    console.log("✅ Backend processing started successfully:", result);
  } catch (error) {
    console.error("❌ Failed to start backend processing:", error);
    // DO NOT THROW - just log the error
    console.error("❌ Backend processing failed, but job remains in queue for retry");
  }
}
