import { generateObject } from "ai";
import { z } from "zod";
import { groq } from "@ai-sdk/groq";
import { NextRequest, NextResponse } from "next/server";
import { createQuiz } from "@/actions/quiz.actions";

// Define the quiz schema with different question types
const quizSchema = z.object({
  title: z.string(),
  description: z.string(),
  questions: z.array(
    z.discriminatedUnion("type", [
      // Multiple choice questions
      z.object({
        type: z.literal("multiple-choice"),
        question: z.string(),
        options: z.array(z.string()),
        correctAnswer: z.number(),
        explanation: z.string(),
        difficulty: z.enum(["easy", "medium", "hard"]),
      }),
      // True/False questions
      z.object({
        type: z.literal("true-false"),
        question: z.string(),
        correctAnswer: z.boolean(),
        explanation: z.string(),
        difficulty: z.enum(["easy", "medium", "hard"]),
      }),
      // Fill in the blank questions
      z.object({
        type: z.literal("fill-blank"),
        question: z.string(),
        correctAnswer: z.string(),
        explanation: z.string(),
        difficulty: z.enum(["easy", "medium", "hard"]),
      }),
    ])
  ),
});

export async function POST(request: NextRequest) {
  try {
    const { title, content, userId, topicName } = await request.json();

    if (!content) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    const quiz = await generateObject({
      model: groq("meta-llama/llama-4-scout-17b-16e-instruct"),
      schemaName: "Quiz",
      schema: quizSchema,
      system: `You are given a narrative script that explains concepts related on a given set of topics related to a major topic. Your task is to generate a comprehensive quiz based on the content of the script. The quiz should include various types of questions to test the learner's understanding of the material. Based strictly on the content of the script, generate a comprehensive quiz with questions that:

      Include a mix of:

      1) Multiple-choice questions (type: 'multiple-choice')
        - 4 answer options per question
        - Only one correct answer per question
        - correctAnswer: index of the correct option (0-3)
        - Include a detailed explanation
        - Assign a difficulty: easy, medium, or hard

      2) True/False questions (type: 'true-false')
        - correctAnswer: true or false
        - Include a detailed explanation
        - Assign a difficulty: easy, medium, or hard

      3) Fill-in-the-blank questions (type: 'fill-blank')
        - A sentence with a blank (_____) for the learner to complete
        - correctAnswer: the word or phrase that fills the blank
        - Include a detailed explanation
        - Assign a difficulty: easy, medium, or hard

      📌 Guidelines
        - The number of questions should depend strictly on the script's length and content depth (can be 5 or fewer, or 15+ if content is long).
        - Ensure a balanced mix of easy, medium, and hard questions.

      Questions should:

        - Be based only on the provided script
        - Test both factual recall and conceptual understanding
        - Be clear, concise, and unambiguous
        - Be suitable for students learning networking concepts`,
      prompt: `Generate a quiz on the topic "${title}" based on the content provided in the script. The script is as follows: ${content}`,
    });

    const quizObj = "object" in quiz ? quiz.object : quiz;
    const validatedQuiz = quizSchema.safeParse(quizObj);

    const quizData = await createQuiz({
      userId,
      prompt: title,
      quiz: validatedQuiz.success ? validatedQuiz.data : quizObj,
      topicName,
    });

    if (!quizData) {
      return NextResponse.json(
        { error: "Failed to create quiz" },
        { status: 500 }
      );
    }

    console.log("Quiz generated successfully:", quizData);
    return NextResponse.json(quizData, { status: 200 });
  } catch (error) {
    console.error("Error generating quiz:", error);
    return NextResponse.json(
      { error: "Failed to generate quiz" },
      { status: 500 }
    );
  }
}
