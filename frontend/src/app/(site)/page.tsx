import CTA1 from "@/components/mvpblocks/cta-1";
import Faq2 from "@/components/mvpblocks/faq-2";
import Feature1 from "@/components/mvpblocks/feature-1";
import Footer4Col from "@/components/mvpblocks/footer-4col";
import Testimonials from "@/components/mvpblocks/testimonials-marquee";
import GradientBlur from "@/components/shared/GradientBlur";
import Hero from "@/components/shared/Hero";
import { NavbarDemo } from "@/components/shared/navbar";

export default function SitePage() {
  return (
    <>
      <NavbarDemo />
      <GradientBlur />
      <div
        className="absolute inset-x-0 top-[360px] h-[250px]"
        style={{
          background: `
            repeating-linear-gradient(
              to right,
              color-mix(in oklab, var(--color-fd-primary) 10%, transparent),
              color-mix(in oklab, var(--color-fd-primary) 10%, transparent) 1px,
              transparent 1px,
              transparent 50px
            ),
            repeating-linear-gradient(
              to bottom,
              color-mix(in oklab, var(--color-fd-primary) 10%, transparent),
              color-mix(in oklab, var(--color-fd-primary) 10%, transparent) 1px,
              transparent 1px,
              transparent 50px
            )
          `,
        }}
      ></div>
      <Hero />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <Feature1 />
        <Testimonials />
        <CTA1 />
        <Faq2 />
      </div>
      <Footer4Col />
    </>
  );
}
