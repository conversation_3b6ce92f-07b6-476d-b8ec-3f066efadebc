"use client";

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';

interface GenerationState {
  isGenerating: boolean;
  currentEntryId: string | null;
  videoJobId: string | null;
  queuePosition: number | null;
  isCompleted: boolean;
  completedVideoUrl: string | null;
}

interface GenerationContextType {
  state: GenerationState;
  startGeneration: (entryId: string, jobId: string, queuePosition?: number) => void;
  stopGeneration: () => void;
  updateQueuePosition: (position: number) => void;
  markCompleted: (videoUrl: string) => void;
  resetState: () => void;
}

const GenerationContext = createContext<GenerationContextType | undefined>(undefined);

const initialState: GenerationState = {
  isGenerating: false,
  currentEntryId: null,
  videoJobId: null,
  queuePosition: null,
  isCompleted: false,
  completedVideoUrl: null,
};

export function GenerationProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<GenerationState>(initialState);

  // Listen for completion notifications
  useEffect(() => {
    const handleCompletion = (event: CustomEvent) => {
      const { entryId, videoUrl } = event.detail;
      
      if (state.currentEntryId === entryId) {
        setState(prev => ({
          ...prev,
          isGenerating: false,
          isCompleted: true,
          completedVideoUrl: videoUrl,
        }));
        
        toast.success("Video generation completed!", {
          description: "Your video is ready to view in the library.",
          action: {
            label: "View Library",
            onClick: () => window.location.href = "/library"
          }
        });
      }
    };

    window.addEventListener('videoCompleted', handleCompletion as EventListener);
    
    return () => {
      window.removeEventListener('videoCompleted', handleCompletion as EventListener);
    };
  }, [state.currentEntryId]);

  const startGeneration = useCallback((entryId: string, jobId: string, queuePosition?: number) => {
    setState({
      isGenerating: true,
      currentEntryId: entryId,
      videoJobId: jobId,
      queuePosition: queuePosition || null,
      isCompleted: false,
      completedVideoUrl: null,
    });
  }, []);

  const stopGeneration = useCallback(() => {
    setState(prev => ({
      ...prev,
      isGenerating: false,
    }));
  }, []);

  const updateQueuePosition = useCallback((position: number) => {
    setState(prev => ({
      ...prev,
      queuePosition: position,
    }));
  }, []);

  const markCompleted = useCallback((videoUrl: string) => {
    setState(prev => ({
      ...prev,
      isGenerating: false,
      isCompleted: true,
      completedVideoUrl: videoUrl,
    }));
  }, []);

  const resetState = useCallback(() => {
    setState(initialState);
  }, []);

  const value: GenerationContextType = {
    state,
    startGeneration,
    stopGeneration,
    updateQueuePosition,
    markCompleted,
    resetState,
  };

  return (
    <GenerationContext.Provider value={value}>
      {children}
    </GenerationContext.Provider>
  );
}

export function useGeneration() {
  const context = useContext(GenerationContext);
  if (context === undefined) {
    throw new Error('useGeneration must be used within a GenerationProvider');
  }
  return context;
}

// Helper function to dispatch completion events
export function dispatchVideoCompletion(entryId: string, videoUrl: string) {
  const event = new CustomEvent('videoCompleted', {
    detail: { entryId, videoUrl }
  });
  window.dispatchEvent(event);
}
