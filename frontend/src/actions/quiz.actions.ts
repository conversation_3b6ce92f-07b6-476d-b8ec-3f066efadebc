"use server"

import { db } from "@/lib/db";
import { quizSchema, Quiz } from "@/lib/quizSchema";

export async function createQuiz(data: {
  userId: string;
  prompt: string;
  quiz: Quiz;
  topicName: string
}) {
  const parseResult = quizSchema.safeParse(data.quiz);

  if (!parseResult.success) {
    console.error("Invalid quiz format", parseResult.error.format());
    throw new Error("Invalid quiz format");
  }

  const validatedQuiz: Quiz = parseResult.data;

  // Convert correctAnswer to string for each question
  const quizForDb = {
    ...validatedQuiz,
    questions: validatedQuiz.questions.map((q) => ({
      ...q,
      correctAnswer: String(q.correctAnswer),
    })),
  };

  return await db.entries.create({
    data: {
      userId: data.userId,
      prompt: data.prompt,
      quiz: quizForDb,
      topicName: data.topicName,
    },
  });
}

export async function deleteQuiz(id: string) {
  const entry = await db.entries.findUnique({
    where: { id },
  });
  
  if (!entry) {
    throw new Error("Quiz not found");
  }

  const updated = await db.entries.update({
    where: { id },
    data: {
      quiz: null,
      hasGiven: false,
      marks: null,
    },
  });

  return {
    success: true,
    message: "Quiz removed successfully",
    updated,
  };
}

export async function answeredQuiz(data: {
  id: string;
  marks: number;
}) {
  const entry = await db.entries.findUnique({
    where: { id: data.id },
  });

  if (!entry) {
    throw new Error("Quiz not found");
  }

  const updated = await db.entries.update({
    where: { id: data.id },
    data: {
      hasGiven: true,
      marks: data.marks,
    },
  });

  return {
    success: true,
    message: "Quiz marked as answered",
    updated,
  };
}

export async function getQuizById(id: string) {
  const entry = await db.entries.findUnique({
    where: { id },
  });

  if (!entry) {
    return null;
  }

  if (!entry.quiz) {
    return null;
  }

  // If given
  if (entry.hasGiven) {
    return {
      ...entry,
      quiz: {
        ...entry.quiz,
        questions: entry.quiz.questions.map((q) => ({
          ...q,
          correctAnswer: String(q.correctAnswer), // Ensure correctAnswer is a string
        })),
      },
    };
  }

  return entry;
}

export async function getUserEntries(userId: string) {
  const entries = await db.entries.findMany({
    where: { userId },
    orderBy: { createdAt: "desc" },
  });

  return entries.map((entry) => ({
    ...entry,
    quiz: entry.quiz
      ? {
          ...entry.quiz,
          questions: entry.quiz.questions.map((q) => ({
            ...q,
            correctAnswer: String(q.correctAnswer), // Ensure correctAnswer is a string
          })),
        }
      : null,
  }));
}