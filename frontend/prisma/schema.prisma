generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Account {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.String
  access_token      String? @db.String
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.String
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
}

model Entries {
  id            String       @id @default(auto()) @map("_id") @db.ObjectId
  userId        String
  prompt        String
  videoUrl      String?
  quiz          Quiz?
  hasGiven      Boolean      @default(false)
  marks         Int?
  isGenerating  Boolean      @default(false)
  queuePosition Int?         @default(0)
  topicName     String
  scripts       ScriptItem[] 
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
}

type ScriptItem {
  manim_code  String
  description String
}

type Quiz {
  title       String
  description String
  questions   Question[]
}

type Question {
  type          String
  question      String
  options       String[]
  correctAnswer String
  explanation   String
  difficulty    String
}
